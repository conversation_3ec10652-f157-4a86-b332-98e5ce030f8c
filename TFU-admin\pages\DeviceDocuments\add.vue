<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <uni-forms-item name="materialNo" label="物料编号" required>
        <uni-easyinput placeholder="请输入物料编号" v-model="formData.materialNo" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="childMaterialNo" label="子物料编号">
        <view v-for="(item, index) in formData.childMaterialNo" :key="index" style="display: flex; margin-bottom: 10px;">
          <uni-data-select
            style="flex: 1;"
            placeholder="请选择子物料号"
            collection="DeviceDocuments"
            field="_id as value, materialNo as text, deviceName as label"
            format="{text} - {label}"
            :where="childMaterialNoWhere"
            v-model="formData.childMaterialNo[index]"
            :clear="true"
          ></uni-data-select>
          <button @click="removeChildMaterial(index)" type="warn" size="mini" style="margin-left: 10px;">删除</button>
        </view>
        <button @click="addChildMaterial" type="primary" size="mini">添加子物料号</button>
      </uni-forms-item>
      <uni-forms-item name="deviceName" label="设备名称">
        <uni-easyinput placeholder="请输入设备名称" v-model="formData.deviceName" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="model" label="型号">
        <uni-easyinput placeholder="请输入设备型号" v-model="formData.model" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="status" label="状态" required>
        <uni-data-checkbox v-model="formData.status" :localdata="formOptions.status_localdata"></uni-data-checkbox>
      </uni-forms-item>
      <uni-forms-item name="documentUrl" label="文档地址" required>
        <uni-easyinput placeholder="请输入文档地址" v-model="formData.documentUrl" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="Type" label="类型">
        <uni-data-checkbox v-model="formData.Type" :localdata="formOptions.Type_localdata"></uni-data-checkbox>
      </uni-forms-item>
      <uni-forms-item name="documentDetails" label="文档详情">
        <view v-for="(item, index) in formData.documentDetails" :key="index" style="display: flex; margin-bottom: 10px;">
          <uni-easyinput placeholder="字段名" v-model="item.name" trim="both" style="flex: 1; margin-right: 10px;"></uni-easyinput>
          <uni-easyinput placeholder="字段值" v-model="item.value" trim="both" style="flex: 1;"></uni-easyinput>
          <button @click="removeDetail(index)" type="warn" size="mini" style="margin-left: 10px;">删除</button>
        </view>
        <button @click="addDetail" type="primary" size="mini">添加文档详情</button>
      </uni-forms-item>
      <uni-forms-item name="version" label="版本">
        <uni-easyinput placeholder="请输入版本" v-model="formData.version" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="uploadBy" label="上传人">
        <uni-easyinput placeholder="请输入上传人" v-model="formData.uploadBy" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="description" label="描述">
        <uni-easyinput placeholder="请输入描述" v-model="formData.description" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="createDate" label="创建日期">
        <uni-datetime-picker return-type="timestamp" v-model="formData.createDate"></uni-datetime-picker>
      </uni-forms-item>
      <uni-forms-item name="updateDate" label="更新日期">
        <uni-datetime-picker return-type="timestamp" v-model="formData.updateDate"></uni-datetime-picker>
      </uni-forms-item>
      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/DeviceDocuments.js';

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'DeviceDocuments';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "materialNo": "",
        "childMaterialNo": [],
        "deviceName": "",
        "model": "",
        "Type": "",
        "status": "",
        "documentUrl": "",
        "documentDetails": [],
        "version": "",
        "uploadBy": ""
      }
      return {
        formData,
        formOptions: {
          "status_localdata": [
            { "value": "有效", "text": "有效" },
            { "value": "无效", "text": "无效" },
            { "value": "更新中", "text": "更新中" },
            { "value": "已归档", "text": "已归档" },
            { "value": "待审核", "text": "待审核" }
          ],
          "Type_localdata": [
            { "value": "零件", "text": "零件" },
            { "value": "整机", "text": "整机" },
            { "value": "图纸", "text": "图纸" },
            { "value": "使用手册", "text": "使用手册" },
            { "value": "维修手册", "text": "维修手册" },
            { "value": "测试报告", "text": "测试报告" },
            { "value": "质检报告", "text": "质检报告" },
            { "value": "其他", "text": "其他" }
          ]
        },
        rules: {
          materialNo: validator.materialNo,
          childMaterialNo: validator.childMaterialNo,  // 更新为 childMaterialNo
          deviceName: validator.deviceName,
          model: validator.model,
          Type: validator.Type,
          status: validator.status,
          documentUrl: validator.documentUrl,
          documentDetails: validator.documentDetails,
          version: validator.version,
          uploadBy: validator.uploadBy
        }
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    watch: {
      'formData.childMaterialNo': {
        deep: true,
        handler(newVal) {
          console.log('添加页面-子物料号数组变化:', JSON.stringify(newVal));
        }
      }
    },
    computed: {
      // 添加计算属性，用于过滤掉当前正在添加的物料编号
      childMaterialNoWhere() {
        if (this.formData.materialNo) {
          const where = `materialNo != '${this.formData.materialNo}'`;
          console.log('添加页面-childMaterialNoWhere 更新为:', where);
          return where;
        }
        console.log('添加页面-childMaterialNoWhere 为空');
        return '';
      }
    },
    methods: {
      addChildMaterial() {
        
        this.formData.childMaterialNo.push('');
        console.log('添加页面-添加子物料号后:', JSON.stringify(this.formData.childMaterialNo));
        
      },
      removeChildMaterial(index) {
        
        console.log('添加页面-移除子物料号前:', JSON.stringify(this.formData.childMaterialNo));
        // 检查索引是否有效
        if (index >= 0 && index < this.formData.childMaterialNo.length) {
          this.formData.childMaterialNo.splice(index, 1);
          console.log('添加页面-移除子物料号后:', JSON.stringify(this.formData.childMaterialNo));
        } else {
          console.log('添加页面-警告：尝试移除无效索引的子物料号');
        }
      },
      addDetail() {
        // 使用深拷贝确保数据独立性
        const newDetail = { name: '', value: '' }
        const updatedDetails = JSON.parse(JSON.stringify([...this.formData.documentDetails, newDetail]))
        this.formData.documentDetails = updatedDetails
      },
      
      /**
       * 过滤空的文档详情项
       */
      filterEmptyDetails() {
        if (!this.formData.documentDetails || !this.formData.documentDetails.length) return
        // 创建一个深拷贝，确保我们处理的是独立的数据
        const detailsCopy = JSON.parse(JSON.stringify(this.formData.documentDetails))
        const filteredDetails = detailsCopy.filter(item => item.name && item.value)
        // 使用深拷贝确保数据独立性
        this.formData.documentDetails = JSON.parse(JSON.stringify(filteredDetails))
      },
      removeDetail(idx) {
        // 使用深拷贝确保数据独立性
        const updatedDetails = JSON.parse(JSON.stringify([
          ...this.formData.documentDetails.slice(0, idx),
          ...this.formData.documentDetails.slice(idx + 1)
        ]))
        this.formData.documentDetails = updatedDetails
      },
      
      /**
       * 检查是否有未完成的自定义字段
       */
      checkIncompleteFields() {
        return this.formData.documentDetails.some(item =>
          (item.name && !item.value) || (!item.name && item.value)
        );
      },
      
      /**
       * 验证表单并提交
       */
      submit() {
        if (this.checkIncompleteFields()) {
          uni.showModal({
            title: '提示',
            content: '有未完成的自定义字段，请填写完整或删除',
            showCancel: false
          });
          return;
        }
        // 过滤掉完全空的自定义字段
        this.formData.documentDetails = this.formData.documentDetails.filter(item => item.name || item.value);
        
        uni.showLoading({
          mask: true
        });
        this.$refs.form.validate().then((res) => {
          // 合并 formData，确保所有字段都能提交
          const submitData = {
            ...this.formData,
            ...res
          };
          // 深拷贝 documentDetails，防止引用问题
          submitData.documentDetails = JSON.parse(JSON.stringify(this.formData.documentDetails));
        
          
          // 添加日志，查看最终提交的数据
          console.log('添加页面-最终提交的数据:', JSON.stringify(submitData));
          
          this.submitForm(submitData);
        }).catch(() => {}).finally(() => {
          uni.hideLoading();
        });
      },

      /**
       * 提交表单
       */
      submitForm(value) {
        console.log('添加页面-开始提交表单，数据:', JSON.stringify(value));
        // 使用 uni-clientDB 提交数据
        return db.collection(dbCollectionName).add(value).then((res) => {
          console.log('添加页面-提交成功，响应:', JSON.stringify(res));
          uni.showToast({
            title: '新增成功'
          });
          // 清除缓存，确保下次获取数据时是最新的
          try {
            uni.removeStorageSync('uni-data-select-cache-DeviceDocuments');
            console.log('添加页面-清除 DeviceDocuments 缓存成功');
          } catch (e) {
            console.error('添加页面-清除缓存失败:', e);
          }
          this.getOpenerEventChannel().emit('refreshData');
          uni.navigateBack();
        }).catch((err) => {
          console.error('添加页面-提交失败，错误:', JSON.stringify(err));
          uni.showModal({
            title: '提交失败',
            content: err.message || '请求服务失败',
            showCancel: false
          });
        }).finally(() => {
          uni.hideLoading();
        });
      },
    }
  }
</script>
