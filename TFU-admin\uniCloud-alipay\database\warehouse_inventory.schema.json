{"bsonType": "object", "required": ["department", "category", "region", "location", "quantity", "itemNumber", "model", "name", "function"], "permission": {"read": true, "create": true, "update": true, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "department": {"bsonType": "string", "title": "所属部门", "description": "物品所属的部门", "foreignKey": "opendb-department.name"}, "classify": {"bsonType": "string", "title": "分类", "description": "物品的分类", "foreignKey": "warehouse_classify.classify"}, "area": {"bsonType": "string", "title": "区域", "description": "存储区域", "foreignKey": "warehouse_area.area"}, "location": {"bsonType": "string", "title": "库位", "description": "具体库位编号", "foreignKey": "warehouse_location.location"}, "quantity": {"bsonType": "int", "title": "当前数量", "description": "物品当前库存数量"}, "materialNo": {"bsonType": "string", "title": "物料号", "description": "物品的编号"}, "model": {"bsonType": "string", "title": "型号", "description": "设备的型号"}, "name": {"bsonType": "string", "title": "中文品名", "description": "物品的名称"}, "englishName": {"bsonType": "string", "title": "英文品名", "description": "物品的英文名称"}, "function": {"bsonType": "string", "title": "功能", "description": "物品的功能描述"}, "remarks": {"bsonType": "string", "title": "备注", "description": "其他备注信息", "defaultValue": ""}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_date": {"bsonType": "timestamp", "description": "更新时间", "forceDefaultValue": {"$env": "now"}}}}