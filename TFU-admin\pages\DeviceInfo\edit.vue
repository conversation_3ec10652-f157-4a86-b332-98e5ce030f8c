<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <uni-forms-item name="deviceNo" label="设备编号" required>
        <uni-easyinput placeholder="设备编号" v-model="formData.deviceNo"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="materialNo_id" label="物料号" required>
        <uni-data-select placeholder="请选择物料号" collection="DeviceDocuments" field="_id as value, materialNo as text" v-model="formData.materialNo_id" @change="onMaterialNoChange"></uni-data-select>
      </uni-forms-item>
      <uni-forms-item name="deviceName" label="设备名称">
        <uni-easyinput placeholder="设备名称" v-model="formData.deviceName" disabled></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="model" label="型号">
        <uni-easyinput placeholder="请输入设备型号" v-model="formData.model" trim="both"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="department" label="所属部门" required>
        <uni-data-select placeholder="请选择所属部门" collection="opendb-department" field="_id as value, name as text" v-model="formData.department" @change="onDepartmentChange"></uni-data-select>
      </uni-forms-item>
      <uni-forms-item name="area" label="区域" required>
        <uni-easyinput placeholder="区域" v-model="formData.area"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="status" label="设备状态" required>
        <uni-data-checkbox v-model="formData.status" :localdata="formOptions.status_localdata"></uni-data-checkbox>
      </uni-forms-item>

      <uni-forms-item name="remark" label="备注">
        <uni-easyinput placeholder="备注" v-model="formData.remark"></uni-easyinput>
      </uni-forms-item>

      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/DeviceInfo.js';

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'DeviceInfo';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "deviceNo": "",
        "materialNo_id": "",
        "deviceName": "",
        "model": "",
        "department": "",
        "area": "",
        "status": "",
        "remark": "",

      }
      return {
        formData,
        formOptions: {
          "status_localdata": [
            {
              "value": "正常",
              "text": "正常"
            },
            {
              "value": "维修中",
              "text": "维修中"
            },
            {
              "value": "待检修",
              "text": "待检修"
            },
            {
              "value": "报废",
              "text": "报废"
            },
            {
              "value": "闲置",
              "text": "闲置"
            },
            {
              "value": "借出",
              "text": "借出"
            }
          ]
        },
        rules: {
          ...getValidator(Object.keys(formData))
        }
      }
    },
    onLoad(e) {
      if (e.id) {
        const id = e.id
        this.formDataId = id
        this.getDetail(id)
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      /**
       * 物料号变更时自动获取设备名称
       */
      onMaterialNoChange(e) {
        
            // 查询对应的设备名称 - 使用对象形式的查询条件
            db.collection('DeviceDocuments')
              .doc(e)
              .field('deviceName')
              .get()
              .then(res => {
                  this.formData.deviceName = res.result.data[0].deviceName;
                
              })
              .catch(err => {
                console.error('获取设备名称失败', err);
          })
        
      },
      
      /**
       * 部门变更时处理
       */
      onDepartmentChange(e) {
        console.log(e)
      },
      
      /**
       * 验证表单并提交
       */
      submit() {
        uni.showLoading({
          mask: true
        })
        this.$refs.form.validate().then((res) => {
          return this.submitForm(res)
        }).catch(() => {
        }).finally(() => {
          uni.hideLoading()
        })
      },

      /**
       * 提交表单
       */
      submitForm(value) {
        // 使用 clientDB 提交数据
        value.updateDate = Date.now();
        return db.collection(dbCollectionName).doc(this.formDataId).update(value).then((res) => {
          uni.showToast({
            title: '修改成功'
          })
          this.getOpenerEventChannel().emit('refreshData')
          setTimeout(() => uni.navigateBack(), 500)
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        })
      },

      /**
       * 获取表单数据
       * @param {Object} id
       */
      getDetail(id) {
        uni.showLoading({
          mask: true
        })
        db.collection(dbCollectionName).doc(id).field("deviceNo,materialNo_id,deviceName,model,department,area,status,remark,createDate,updateDate").get().then((res) => {
          const data = res.result.data[0]
          if (data) {
            this.formData = data
            console.log(this.formData)
          }
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        }).finally(() => {
          uni.hideLoading()
        })
      }
    }
  }
</script>
