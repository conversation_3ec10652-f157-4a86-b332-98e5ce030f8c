<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="materialNo" label="物料编号" required>
				<uni-easyinput placeholder="请输入物料编号" v-model="formData.materialNo" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="childMaterialNo" label="子物料编号">
				<view v-for="(item, index) in formData.childMaterialNo" :key="index" style="display: flex; margin-bottom: 10px;">
					<uni-data-select
						style="flex: 1;"
						placeholder="请选择子物料号"
						collection="DeviceDocuments"
						field="_id as value, materialNo as text, deviceName as label"
						format="{text} - {label}"
						:where="childMaterialNoWhere"
						v-model="formData.childMaterialNo[index]"
						:clear="true"
					></uni-data-select>
					<button @click="removeChildMaterial(index)" type="warn" size="mini" style="margin-left: 10px;">删除</button>
				</view>
				<button @click="addChildMaterial" type="primary" size="mini">添加子物料号</button>
			</uni-forms-item>
			<uni-forms-item name="deviceName" label="设备名称">
				<uni-easyinput placeholder="设备名称" v-model="formData.deviceName"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="model" label="型号">
				<uni-easyinput placeholder="请输入设备型号" v-model="formData.model" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="status" label="状态" required>
				<uni-data-checkbox v-model="formData.status"
					:localdata="formOptions.status_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="documentUrl" label="文档地址" required>
				<uni-easyinput placeholder="资料链接地址" v-model="formData.documentUrl"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="Type" label="类型" required>
				<uni-data-checkbox v-model="formData.Type" :localdata="formOptions.Type_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="documentDetails" label="文档详情">
				<view v-if="formData.documentDetails.length === 0"
					style="margin-bottom:10px;color:#999;font-size:14px;">
					暂无自定义字段，点击下方按钮添加
				</view>
				<view v-for="(item, idx) in formData.documentDetails" :key="idx"
					style="display:flex;align-items:center;margin-bottom:15px;">
					<uni-easyinput placeholder="字段名" v-model="item.name" style="flex:1;margin-right:10px;" />
					<uni-easyinput placeholder="字段值" v-model="item.value" style="flex:1;margin-right:10px;" />
					<button type="warn" size="mini" @click="removeDetail(idx)">删除</button>
				</view>
				<button type="primary" @click="addDetail" style="margin-top:10px;width:100%;">
					添加自定义字段
				</button>
			</uni-forms-item>
			<uni-forms-item name="version" label="版本">
				<uni-easyinput placeholder="资料版本号" v-model="formData.version"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="uploadBy" label="上传人">
				<uni-easyinput placeholder="上传人" v-model="formData.uploadBy"></uni-easyinput>
			</uni-forms-item>
			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/DeviceDocuments.js';

	const db = uniCloud.database();
	const dbCollectionName = 'DeviceDocuments';


	export default {
		data() {
			return {
				formData: {
					materialNo: '',
					childMaterialNo: [],
					deviceName: '',
					model: '',
					department: '',
					area: '',
					status: '',
					remark: '',
					createDate: '',
					updateDate: '',
					version: '',
					uploadBy: '',
					Type: [],
					documentDetails: [],
					documentUrl: ''
				},
				formDataId: '',
				childMaterialNoWhere: '',
				formOptions: {
					status_localdata: [{
							value: "有效",
							text: "有效"
						},
						{
							value: "无效",
							text: "无效"
						},
						{
							value: "更新中",
							text: "更新中"
						},
						{
							value: "已归档",
							text: "已归档"
						},
						{
							value: "待审核",
							text: "待审核"
						}
					],
					Type_localdata: [{
							value: "零件",
							text: "零件"
						},
						{
							value: "整机",
							text: "整机"
						},
						{
							value: "图纸",
							text: "图纸"
						},
						{
							value: "使用手册",
							text: "使用手册"
						},
						{
							value: "维修手册",
							text: "维修手册"
						},
						{
							value: "测试报告",
							text: "测试报告"
						},
						{
							value: "质检报告",
							text: "质检报告"
						},
						{
							value: "其他",
							text: "其他"
						}
					]
				},
				rules: validator
			}
		},
		onLoad(e) {
			if (e.id) {
				this.formDataId = e.id;
				this.getDetail(e.id);
				this.childMaterialNoWhere = `_id != "${e.id}"`;
			}
		},
		onReady() {
			this.$refs.form.setRules(this.rules);
		},
		watch: {
			'formData.materialNo': {
				immediate: true,
				handler(newVal) {
					if (newVal) {
						this.childMaterialNoWhere = `materialNo != "${newVal}"`;
					} else if (this.formDataId) {
						this.childMaterialNoWhere = `_id != "${this.formDataId}"`;
					} else {
						this.childMaterialNoWhere = '';
					}
					console.log('childMaterialNoWhere 更新为:', this.childMaterialNoWhere);
				}
			},
			'formData.childMaterialNo': {
				deep: true,
				handler(newVal) {
					console.log('子物料号数组变化:', JSON.stringify(newVal));
				}
			}
		},
		methods: {
			addChildMaterial() {
				
				console.log('添加子物料号前:', JSON.stringify(this.formData.childMaterialNo));
				
				// 添加一个新的子物料号输入框
				this.formData.childMaterialNo.push('');
				
				
				console.log('添加子物料号后:', JSON.stringify(this.formData.childMaterialNo));
			},
			removeChildMaterial(index) {
				
				console.log('移除子物料号前:', JSON.stringify(this.formData.childMaterialNo));
				console.log('移除索引:', index);
				// 检查索引是否有效
				if (index >= 0 && index < this.formData.childMaterialNo.length) {
					this.formData.childMaterialNo.splice(index, 1);
					console.log('移除子物料号后:', JSON.stringify(this.formData.childMaterialNo));
				} else {
					console.log('警告：尝试移除无效索引的子物料号');
				}
			},
			addDetail() {
				this.formData.documentDetails.push({
					name: '',
					value: ''
				});
			},
			removeDetail(idx) {
				this.formData.documentDetails.splice(idx, 1);
			},
			checkIncompleteFields() {
				return this.formData.documentDetails.some(item =>
					(item.name && !item.value) || (!item.name && item.value)
				);
			},
			submit() {
					if (this.checkIncompleteFields()) {
						uni.showModal({
							title: '提示',
							content: '有未完成的自定义字段，请填写完整或删除',
							showCancel: false
						});
						return;
					}
					// 过滤掉完全空的自定义字段
					this.formData.documentDetails = this.formData.documentDetails.filter(item => item.name || item.value);
					
					

					uni.showLoading({
						mask: true
					});
					this.$refs.form.validate().then((res) => {
						const submitData = JSON.parse(JSON.stringify({
							...this.formData,
							...res
						}));
						
						
						this.submitForm(submitData);
					}).catch(() => {}).finally(() => {
						uni.hideLoading();
					});
				},
			submitForm(value) {
				
				console.log('submitForm 最终处理后的子物料号:', JSON.stringify(value.childMaterialNo));
				
				// 使用 uni-clientDB 提交数据
				db.collection(dbCollectionName).doc(this.formDataId).update(value).then((res) => {
					console.log('提交成功，响应:', JSON.stringify(res));
					uni.showToast({
						title: '修改成功'
					});
					// 清除缓存，确保下次获取数据时是最新的
				try {
						uni.removeStorageSync('uni-data-select-cache-DeviceDocuments');
						console.log('清除 DeviceDocuments 缓存成功');
					} catch (e) {
						console.error('清除缓存失败:', e);
					}
					this.getOpenerEventChannel().emit('refreshData');
					uni.navigateBack();
				}).catch((err) => {
					console.error('提交失败，错误:', JSON.stringify(err));
					uni.showModal({
						title: '提交失败',
						content: err.message || '请求服务失败',
						showCancel: false
					});
				}).finally(() => {
					uni.hideLoading();
				});
			},
			getDetail(id) {
				uni.showLoading({
					mask: true
				});
				db.collection(dbCollectionName).doc(id)
					.field(
						"materialNo,childMaterialNo,deviceName,model,status,documentUrl,Type,documentDetails,version,uploadBy")
					.get().then((res) => {
						this.formData = res.result.data[0] || {};
						
						
						
						console.log('获取到的详情数据:', JSON.stringify(this.formData));
					}).catch((err) => {
						uni.showModal({
							title: '获取数据失败',
							content: err.message || '请求服务失败',
							showCancel: false
						});
					}).finally(() => {
						uni.hideLoading();
					});
			}
		}
	}
</script>