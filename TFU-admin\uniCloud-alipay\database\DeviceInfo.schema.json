{"bsonType": "object", "required": ["deviceNo", "materialNo", "department", "area", "status"], "permission": {"read": true, "create": true, "update": true, "delete": true}, "properties": {"_id": {"description": "ID，系统自动生成"}, "deviceNo": {"bsonType": "string", "description": "设备编号", "title": "设备编号"}, "materialNo_id": {"bsonType": "string", "description": "物料号", "title": "物料号", "foreignKey": "DeviceDocuments._id"}, "deviceName": {"bsonType": "string", "description": "设备名称", "title": "设备名称", "foreignKey": "DeviceDocuments.deviceName"}, "model": {"bsonType": "string", "title": "型号", "description": "设备的型号"}, "department": {"bsonType": "string", "description": "所属部门", "title": "所属部门", "foreignKey": "opendb-department.name"}, "area": {"bsonType": "string", "description": "区域", "title": "区域"}, "status": {"bsonType": "string", "description": "设备状态", "title": "设备状态", "enum": ["正常", "维修中", "待检修", "报废", "闲置", "借出"]}, "remark": {"bsonType": "string", "description": "备注", "title": "备注"}, "createDate": {"bsonType": "timestamp", "description": "创建时间"}, "updateDate": {"bsonType": "timestamp", "description": "更新时间"}}}