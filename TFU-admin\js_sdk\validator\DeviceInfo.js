// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "deviceNo": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "设备编号",
    "label": "设备编号"
  },
  "materialNo_id": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "物料号",
    "label": "物料号"
  },
  "deviceName": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "设备名称",
    "label": "设备名称"
  },
  "model": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "型号",
    "label": "型号"
  },
  "department": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "所属部门",
    "label": "所属部门"
  },
  "area": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "区域",
    "label": "区域"
  },
  "status": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "正常",
            "text": "正常"
          },
          {
            "value": "维修中",
            "text": "维修中"
          },
          {
            "value": "待检修",
            "text": "待检修"
          },
          {
            "value": "报废",
            "text": "报废"
          },
          {
            "value": "闲置",
            "text": "闲置"
          },
          {
            "value": "借出",
            "text": "借出"
          }
        ]
      }
    ],
    "title": "设备状态",
    "label": "设备状态"
  },
  "remark": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "备注",
    "label": "备注"
  },
  "createDate": {
    "rules": [
      {
        "format": "timestamp"
      }
    ]
  },
  "updateDate": {
    "rules": [
      {
        "format": "timestamp"
      }
    ]
  }
}

const enumConverter = {
  "status_valuetotext": {
    "正常": "正常",
    "维修中": "维修中",
    "待检修": "待检修",
    "报废": "报废",
    "闲置": "闲置",
    "借出": "借出"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
