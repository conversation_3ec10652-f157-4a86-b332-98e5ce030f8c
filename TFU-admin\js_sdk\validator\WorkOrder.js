// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "orderNo": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "工单编号",
    "label": "工单编号"
  },
  "childOrderNo": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "子工单编号",
    "label": "子工单编号"
  },
  "orderDisplay": {
    "rules": [
      {
        "format": "bool"
      }
    ],
    "title": "显示",
    "label": "显示"
  },
  "deviceNo": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "设备编号",
    "label": "设备编号"
  },
  "department": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "所属部门",
    "label": "所属部门"
  },
  "area": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "区域",
    "label": "区域"
  },
  "status": {
    "rules": [
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "待处理",
            "text": "待处理"
          },
          {
            "value": "处理中",
            "text": "处理中"
          },
          {
            "value": "已完成",
            "text": "已完成"
          },
          {
            "value": "已关闭",
            "text": "已关闭"
          },
          {
            "value": "已取消",
            "text": "已取消"
          },
          {
            "value": "待评估",
            "text": "待评估"
          },
          {
            "value": "待验收",
            "text": "待验收"
          }
        ]
      }
    ],
    "title": "工单状态",
    "label": "工单状态"
  },
  "priority": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "高",
            "text": "高"
          },
          {
            "value": "中",
            "text": "中"
          },
          {
            "value": "低",
            "text": "低"
          }
        ]
      }
    ],
    "title": "优先级",
    "label": "优先级"
  },
  "creator": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "工单创建人",
    "label": "工单创建人"
  },
  "creatorDept": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "创建人部门",
    "label": "创建人部门"
  },
  "assignedTo": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "处理人",
    "label": "处理人"
  },
  "assignedToDept": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "处理人部门",
    "label": "处理人部门"
  },
  "description": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "工单描述",
    "label": "工单描述"
  },
  "workType": {
    "rules": [
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "维修",
            "text": "维修"
          },
          {
            "value": "预防性维修",
            "text": "预防性维修"
          },
          {
            "value": "保养",
            "text": "保养"
          },
          {
            "value": "检查",
            "text": "检查"
          },
          {
            "value": "安装",
            "text": "安装"
          },
          {
            "value": "调试",
            "text": "调试"
          },
          {
            "value": "其他",
            "text": "其他"
          }
        ]
      }
    ],
    "title": "工单类型",
    "label": "工单类型"
  },
  "withMaterialNo": {
    "rules": [
      {
        "format": "array"
      }
    ]
  },
  "planStartTime": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "工单计划开始时间",
    "label": "工单计划开始时间"
  },
  "expectedTime": {
    "rules": [
      {
        "format": "int"
      }
    ],
    "title": "预计完成时间(小时)",
    "label": "预计完成时间(小时)"
  },
  "actualTime": {
    "rules": [
      {
        "format": "int"
      }
    ],
    "title": "实际完成时间(小时)",
    "label": "实际完成时间(小时)"
  },
  "attachments": {
    "rules": [
      {
        "format": "array"
      }
    ],
    "title": "附件列表",
    "label": "附件列表"
  },
  "remarks": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "备注",
    "label": "备注"
  },
  "createDate": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "创建时间",
    "label": "创建时间"
  },
  "updateDate": {
    "rules": [
      {
        "format": "timestamp"
      }
    ]
  },
  "startTime": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "开始处理时间",
    "label": "开始处理时间"
  },
  "completeTime": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "完成时间",
    "label": "完成时间"
  },
  "closeTime": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "关闭时间",
    "label": "关闭时间"
  }
}

const enumConverter = {
  "status_valuetotext": {
    "待处理": "待处理",
    "处理中": "处理中",
    "已完成": "已完成",
    "已关闭": "已关闭",
    "已取消": "已取消",
    "待评估": "待评估",
    "待验收": "待验收"
  },
  "priority_valuetotext": {
    "高": "高",
    "中": "中",
    "低": "低"
  },
  "workType_valuetotext": {
    "维修": "维修",
    "预防性维修": "预防性维修",
    "保养": "保养",
    "检查": "检查",
    "安装": "安装",
    "调试": "调试",
    "其他": "其他"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
