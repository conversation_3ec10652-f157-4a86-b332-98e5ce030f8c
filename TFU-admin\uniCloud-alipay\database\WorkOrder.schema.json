{"bsonType": "object", "required": ["deviceNo", "priority", "creator"], "permission": {"read": true, "create": true, "update": true, "delete": true}, "properties": {"_id": {"description": "ID，系统自动生成"}, "orderNo": {"bsonType": "string", "description": "工单编号", "title": "工单编号"}, "childOrderNo": {"bsonType": "string", "description": "子工单编号，对于保养，检查等工单，有可能产生需要维修的子工单", "title": "子工单编号"}, "orderDisplay": {"bsonType": "bool", "description": "工单的是否显示", "title": "显示"}, "deviceNo": {"bsonType": "string", "description": "设备编号", "title": "设备编号", "foreignKey": "DeviceInfo.deviceNo"}, "department": {"bsonType": "string", "description": "所属部门", "title": "所属部门", "foreignKey": "DeviceInfo.department"}, "area": {"bsonType": "string", "description": "区域", "title": "区域", "foreignKey": "DeviceInfo.area"}, "status": {"bsonType": "string", "description": "工单状态", "title": "工单状态", "enum": ["待处理", "处理中", "已完成", "已关闭", "已取消", "待评估", "待验收"]}, "priority": {"bsonType": "string", "description": "优先级", "title": "优先级", "enum": ["高", "中", "低"]}, "creator": {"bsonType": "string", "description": "工单创建人", "title": "工单创建人", "foreignKey": "uni-id-users.username"}, "creatorDept": {"bsonType": "string", "description": "创建人部门", "title": "创建人部门", "foreignKey": "uni-id-users.department_id"}, "assignedTo": {"bsonType": "string", "description": "处理人", "title": "处理人", "foreignKey": "uni-id-users.username"}, "assignedToDept": {"bsonType": "string", "description": "处理人部门", "title": "处理人部门", "foreignKey": "uni-id-users.department_id"}, "description": {"bsonType": "string", "description": "工单描述", "title": "工单描述"}, "workType": {"bsonType": "string", "description": "工单类型", "title": "工单类型", "enum": ["维修", "预防性维修", "保养", "检查", "安装", "调试", "其他"]}, "withMaterialNo": {"bsonType": "array", "description": "如果workType是维修，则需要关联物料号，也可以不关联", "foreignKey": "DeviceDocuments.materialNo"}, "planStartTime": {"bsonType": "timestamp", "description": "工单计划开始时间", "title": "工单计划开始时间"}, "expectedTime": {"bsonType": "int", "description": "预计完成时间(小时)", "title": "预计完成时间(小时)"}, "actualTime": {"bsonType": "int", "description": "实际完成时间(小时)", "title": "实际完成时间(小时)"}, "attachments": {"bsonType": "array", "description": "附件列表", "title": "附件列表", "items": {"bsonType": "object", "properties": {"name": {"bsonType": "string", "description": "文件名称"}, "url": {"bsonType": "string", "description": "文件地址"}, "size": {"bsonType": "int", "description": "文件大小"}, "type": {"bsonType": "string", "description": "文件类型"}, "uploadTime": {"bsonType": "timestamp", "description": "上传时间"}}}}, "remarks": {"bsonType": "string", "description": "备注", "title": "备注"}, "createDate": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间"}, "updateDate": {"bsonType": "timestamp", "description": "更新时间"}, "startTime": {"bsonType": "timestamp", "description": "开始处理时间", "title": "开始处理时间"}, "completeTime": {"bsonType": "timestamp", "description": "完成时间", "title": "完成时间"}, "closeTime": {"bsonType": "timestamp", "description": "关闭时间", "title": "关闭时间"}}}