<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <!-- 工单编号（不可修改） -->
      <uni-forms-item name="orderNo" label="工单编号">
        <uni-easyinput placeholder="工单编号" v-model="formData.orderNo" :disabled="true"></uni-easyinput>
      </uni-forms-item>

      <!-- 子工单编号（不可修改） -->
      <uni-forms-item name="childOrderNo" label="子工单编号">
        <uni-easyinput placeholder="子工单编号" v-model="formData.childOrderNo" :disabled="true"></uni-easyinput>
      </uni-forms-item>

      <!-- 显示 -->
      <uni-forms-item name="orderDisplay" label="显示">
        <switch @change="binddata('orderDisplay', $event.detail.value)" :checked="formData.orderDisplay"></switch>
      </uni-forms-item>

      <!-- 设备编号搜索 -->
      <uni-forms-item name="deviceNo" label="设备编号" required>
        <view class="search-container">
          <uni-easyinput
            placeholder="请输入设备编号进行搜索"
            v-model="deviceSearchText"
            @input="onDeviceSearch"
            @confirm="onDeviceSearchConfirm"
          ></uni-easyinput>
          <button
            type="primary"
            size="mini"
            @click="onDeviceSearchConfirm"
            style="margin-left: 10px;"
          >
            搜索
          </button>
        </view>
        <!-- 设备搜索结果列表 -->
        <view v-if="deviceSearchResults.length > 0" class="search-results">
          <view
            v-for="device in deviceSearchResults"
            :key="device._id"
            class="search-result-item"
            @click="selectDevice(device)"
          >
            <text class="device-no">{{ device.deviceNo }}</text>
            <text class="device-info">{{ device.deviceName }} - {{ device.department }} - {{ device.area }}</text>
          </view>
        </view>
        <!-- 已选择的设备信息显示 -->
        <view v-if="formData.deviceNo" class="selected-device">
          <text>已选择设备：{{ formData.deviceNo }}</text>
        </view>
      </uni-forms-item>

      <!-- 所属部门（自动填充，只读） -->
      <uni-forms-item name="department" label="所属部门">
        <uni-easyinput placeholder="根据设备编号自动填充" v-model="formData.department" :disabled="true"></uni-easyinput>
      </uni-forms-item>

      <!-- 区域（自动填充，只读） -->
      <uni-forms-item name="area" label="区域">
        <uni-easyinput placeholder="根据设备编号自动填充" v-model="formData.area" :disabled="true"></uni-easyinput>
      </uni-forms-item>

      <uni-forms-item name="status" label="工单状态">
        <uni-data-checkbox v-model="formData.status" :localdata="formOptions.status_localdata"></uni-data-checkbox>
      </uni-forms-item>

      <uni-forms-item name="priority" label="优先级" required>
        <uni-data-checkbox v-model="formData.priority" :localdata="formOptions.priority_localdata"></uni-data-checkbox>
      </uni-forms-item>

      <!-- 工单创建人（不可更改） -->
      <uni-forms-item name="creator" label="工单创建人" required>
        <uni-easyinput placeholder="工单创建人" v-model="formData.creator" :disabled="true"></uni-easyinput>
      </uni-forms-item>

      <!-- 创建人部门（自动填充，只读） -->
      <uni-forms-item name="creatorDept" label="创建人部门">
        <uni-easyinput placeholder="创建人部门" v-model="formData.creatorDept" :disabled="true"></uni-easyinput>
      </uni-forms-item>

      <!-- 处理人搜索 -->
      <uni-forms-item name="assignedTo" label="处理人">
        <view class="search-container">
          <uni-easyinput
            placeholder="请输入用户名进行搜索"
            v-model="userSearchText"
            @input="onUserSearch"
            @confirm="onUserSearchConfirm"
          ></uni-easyinput>
          <button
            type="primary"
            size="mini"
            @click="onUserSearchConfirm"
            style="margin-left: 10px;"
          >
            搜索
          </button>
        </view>
        <!-- 用户搜索结果列表 -->
        <view v-if="userSearchResults.length > 0" class="search-results">
          <view
            v-for="user in userSearchResults"
            :key="user._id"
            class="search-result-item"
            @click="selectUser(user)"
          >
            <text class="user-name">{{ user.username || user.nickname }}</text>
            <text class="user-info">{{ user.departmentName || '无部门' }}</text>
          </view>
        </view>
        <!-- 已选择的处理人信息显示 -->
        <view v-if="formData.assignedTo" class="selected-user">
          <text>已选择处理人：{{ formData.assignedTo }}</text>
        </view>
      </uni-forms-item>

      <!-- 处理人部门（自动填充，只读） -->
      <uni-forms-item name="assignedToDept" label="处理人部门">
        <uni-easyinput placeholder="处理人部门" v-model="formData.assignedToDept" :disabled="true"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="description" label="工单描述">
        <uni-easyinput placeholder="工单描述" v-model="formData.description"></uni-easyinput>
      </uni-forms-item>

      <uni-forms-item name="workType" label="工单类型">
        <uni-data-checkbox v-model="formData.workType"
          :localdata="formOptions.workType_localdata"
          @change="onWorkTypeChange"></uni-data-checkbox>
      </uni-forms-item>

      <!-- 关联物料号（仅当工单类型为维修时显示） -->
      <uni-forms-item v-if="formData.workType === '维修'" name="withMaterialNo" label="关联物料号">
        <view class="material-selection">
          <button
            type="default"
            size="mini"
            @click="loadMaterialOptions"
            style="margin-bottom: 10px;"
          >
            加载可选物料
          </button>
          <uni-data-checkbox
            v-if="materialOptions.length > 0"
            v-model="formData.withMaterialNo"
            :localdata="materialOptions"
            :multiple="true"
            :wrap="true"
          ></uni-data-checkbox>
          <view v-else-if="formData.deviceNo" class="no-material-tip">
            <text>暂无可选物料，请先点击"加载可选物料"按钮</text>
          </view>
          <view v-else class="no-device-tip">
            <text>请先选择设备编号</text>
          </view>
        </view>
      </uni-forms-item>

      <uni-forms-item name="planStartTime" label="计划开始时间">
        <uni-datetime-picker
          v-model="formData.planStartTime"
          type="datetime"
          return-type="timestamp"
          placeholder="请选择计划开始时间"
          :clear-icon="true"
        ></uni-datetime-picker>
      </uni-forms-item>

      <uni-forms-item name="expectedTime" label="预计完成时间(小时)">
        <uni-easyinput placeholder="预计完成时间(小时)" type="number" v-model="formData.expectedTime"></uni-easyinput>
      </uni-forms-item>

      <uni-forms-item name="actualTime" label="实际完成时间(小时)">
        <uni-easyinput placeholder="实际完成时间(小时)" type="number" v-model="formData.actualTime"></uni-easyinput>
      </uni-forms-item>

      <uni-forms-item name="attachments" label="附件列表">
        <uni-data-checkbox :multiple="true" v-model="formData.attachments"></uni-data-checkbox>
      </uni-forms-item>

      <uni-forms-item name="remarks" label="备注">
        <uni-easyinput placeholder="备注" v-model="formData.remarks"></uni-easyinput>
      </uni-forms-item>

      <!-- 创建时间（不可修改） -->
      <uni-forms-item name="createDate" label="创建时间">
        <uni-datetime-picker return-type="timestamp" v-model="formData.createDate" :disabled="true"></uni-datetime-picker>
      </uni-forms-item>

      <!-- 更新时间（不可修改） -->
      <uni-forms-item name="updateDate" label="更新时间">
        <uni-datetime-picker return-type="timestamp" v-model="formData.updateDate" :disabled="true"></uni-datetime-picker>
      </uni-forms-item>

      <uni-forms-item name="startTime" label="开始处理时间">
        <uni-datetime-picker return-type="timestamp" v-model="formData.startTime"></uni-datetime-picker>
      </uni-forms-item>

      <uni-forms-item name="completeTime" label="完成时间">
        <uni-datetime-picker return-type="timestamp" v-model="formData.completeTime"></uni-datetime-picker>
      </uni-forms-item>

      <uni-forms-item name="closeTime" label="关闭时间">
        <uni-datetime-picker return-type="timestamp" v-model="formData.closeTime"></uni-datetime-picker>
      </uni-forms-item>
      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/WorkOrder.js';

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'WorkOrder';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "orderNo": "",
        "childOrderNo": "",
        "orderDisplay": null,
        "deviceNo": "",
        "department": "",
        "area": "",
        "status": "",
        "priority": "",
        "creator": "",
        "creatorDept": "",
        "assignedTo": "",
        "assignedToDept": "",
        "description": "",
        "workType": "",
        "withMaterialNo": [],
        "planStartTime": null,
        "expectedTime": null,
        "actualTime": null,
        "attachments": [],
        "remarks": "",
        "createDate": null,
        "updateDate": null,
        "startTime": null,
        "completeTime": null,
        "closeTime": null
      }
      return {
        formData,
        // 搜索相关数据
        deviceSearchText: '',
        deviceSearchResults: [],
        userSearchText: '',
        userSearchResults: [],
        currentUserInfo: null,
        // 物料选择相关数据
        materialOptions: [],
        selectedDeviceInfo: null,
        formOptions: {
          "status_localdata": [
            {
              "value": "待处理",
              "text": "待处理"
            },
            {
              "value": "处理中",
              "text": "处理中"
            },
            {
              "value": "已完成",
              "text": "已完成"
            },
            {
              "value": "已关闭",
              "text": "已关闭"
            },
            {
              "value": "已取消",
              "text": "已取消"
            },
            {
              "value": "待评估",
              "text": "待评估"
            },
            {
              "value": "待验收",
              "text": "待验收"
            }
          ],
          "priority_localdata": [
            {
              "value": "高",
              "text": "高"
            },
            {
              "value": "中",
              "text": "中"
            },
            {
              "value": "低",
              "text": "低"
            }
          ],
          "workType_localdata": [
            {
              "value": "维修",
              "text": "维修"
            },
            {
              "value": "预防性维修",
              "text": "预防性维修"
            },
            {
              "value": "保养",
              "text": "保养"
            },
            {
              "value": "检查",
              "text": "检查"
            },
            {
              "value": "安装",
              "text": "安装"
            },
            {
              "value": "调试",
              "text": "调试"
            },
            {
              "value": "其他",
              "text": "其他"
            }
          ]
        },
        rules: {
          ...getValidator(Object.keys(formData))
        }
      }
    },
    onLoad(e) {
      if (e.id) {
        const id = e.id
        this.formDataId = id
        this.getDetail(id)
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      /**
       * 获取部门名称路径（通用方法）
       */
      async getDepartmentPath(departmentIds) {
        if (!departmentIds || departmentIds.length === 0) {
          return '该用户没有所属部门';
        }

        try {
          // 如果是字符串，转换为数组
          const deptIds = Array.isArray(departmentIds) ? departmentIds : [departmentIds];

          // 获取所有部门信息
          const res = await db.collection('opendb-department')
            .where({
              _id: db.command.in(deptIds)
            })
            .field('_id,name')
            .get();

          if (res.result.data && res.result.data.length > 0) {
            // 创建部门ID到名称的映射
            const deptMap = {};
            res.result.data.forEach(dept => {
              deptMap[dept._id] = dept.name;
            });

            // 按照departmentIds的顺序拼接部门名称
            const deptNames = deptIds.map(id => deptMap[id]).filter(name => name);
            return deptNames.join(' > ');
          } else {
            return '该用户没有所属部门';
          }
        } catch (error) {
          console.error('获取部门信息失败:', error);
          return '获取部门信息失败';
        }
      },

      /**
       * 工单类型变更处理
       */
      onWorkTypeChange(value) {
        // 当工单类型不是维修时，清空关联物料号
        if (value !== '维修') {
          this.formData.withMaterialNo = [];
          this.materialOptions = [];
        }
      },

      /**
       * 加载物料选项
       */
      async loadMaterialOptions() {
        if (!this.formData.deviceNo) {
          uni.showToast({
            title: '请先选择设备编号',
            icon: 'none'
          });
          return;
        }

        try {
          uni.showLoading({ title: '加载物料中...' });

          // 1. 根据设备编号获取设备信息，包括materialNo_id
          const deviceRes = await db.collection('DeviceInfo')
            .where({ deviceNo: this.formData.deviceNo })
            .field('materialNo_id')
            .get();

          if (!deviceRes.result.data || deviceRes.result.data.length === 0) {
            uni.showToast({
              title: '未找到设备信息',
              icon: 'none'
            });
            return;
          }

          const materialNoId = deviceRes.result.data[0].materialNo_id;

          // 2. 根据materialNo_id获取主物料和子物料信息
          const materialRes = await db.collection('DeviceDocuments')
            .doc(materialNoId)
            .field('materialNo,deviceName,childMaterialNo')
            .get();

          if (!materialRes.result.data || materialRes.result.data.length === 0) {
            uni.showToast({
              title: '未找到物料信息',
              icon: 'none'
            });
            return;
          }

          const mainMaterial = materialRes.result.data[0];
          const options = [];

          // 添加主物料
          options.push({
            value: mainMaterial.materialNo,
            text: `${mainMaterial.materialNo} - ${mainMaterial.deviceName}`
          });

          // 3. 如果有子物料，获取子物料信息
          if (mainMaterial.childMaterialNo && mainMaterial.childMaterialNo.length > 0) {
            const childMaterialRes = await db.collection('DeviceDocuments')
              .where({
                _id: db.command.in(mainMaterial.childMaterialNo)
              })
              .field('materialNo,deviceName')
              .get();

            if (childMaterialRes.result.data && childMaterialRes.result.data.length > 0) {
              childMaterialRes.result.data.forEach(child => {
                options.push({
                  value: child.materialNo,
                  text: `${child.materialNo} - ${child.deviceName}`
                });
              });
            }
          }

          this.materialOptions = options;

          if (options.length === 0) {
            uni.showToast({
              title: '该设备暂无可选物料',
              icon: 'none'
            });
          } else {
            uni.showToast({
              title: `加载成功，共${options.length}个物料`,
              icon: 'success'
            });
          }

        } catch (error) {
          console.error('加载物料选项失败:', error);
          uni.showToast({
            title: '加载物料失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      },

      /**
       * 设备编号搜索
       */
      onDeviceSearch() {
        // 实时搜索，可以在这里添加防抖逻辑
        if (this.deviceSearchText.length >= 2) {
          this.searchDevices();
        } else {
          this.deviceSearchResults = [];
        }
      },

      /**
       * 设备编号搜索确认
       */
      onDeviceSearchConfirm() {
        if (this.deviceSearchText.trim()) {
          this.searchDevices();
        }
      },

      /**
       * 搜索设备
       */
      async searchDevices() {
        try {
          uni.showLoading({ title: '搜索中...' });

          const res = await db.collection('DeviceInfo')
            .where({
              deviceNo: new RegExp(this.deviceSearchText, 'i')
            })
            .field('_id,deviceNo,deviceName,department,area')
            .limit(10)
            .get();

          this.deviceSearchResults = res.result.data || [];

          if (this.deviceSearchResults.length === 0) {
            uni.showToast({
              title: '未找到匹配的设备',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('搜索设备失败:', error);
          uni.showToast({
            title: '搜索设备失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      },

      /**
       * 选择设备
       */
      selectDevice(device) {
        this.formData.deviceNo = device.deviceNo;
        this.formData.department = device.department;
        this.formData.area = device.area;

        // 保存设备信息，用于后续物料查询
        this.selectedDeviceInfo = device;

        // 清空搜索结果
        this.deviceSearchResults = [];
        this.deviceSearchText = device.deviceNo;

        // 如果当前工单类型是维修，清空之前的物料选择
        if (this.formData.workType === '维修') {
          this.formData.withMaterialNo = [];
          this.materialOptions = [];
        }

        uni.showToast({
          title: '设备选择成功',
          icon: 'success'
        });
      },

      /**
       * 用户搜索
       */
      onUserSearch() {
        // 实时搜索，可以在这里添加防抖逻辑
        if (this.userSearchText.length >= 2) {
          this.searchUsers();
        } else {
          this.userSearchResults = [];
        }
      },

      /**
       * 用户搜索确认
       */
      onUserSearchConfirm() {
        if (this.userSearchText.trim()) {
          this.searchUsers();
        }
      },

      /**
       * 搜索用户
       */
      async searchUsers() {
        try {
          uni.showLoading({ title: '搜索中...' });

          // 先搜索用户
          const userRes = await db.collection('uni-id-users')
            .where(db.command.or([
              { username: new RegExp(this.userSearchText, 'i') },
              { nickname: new RegExp(this.userSearchText, 'i') }
            ]))
            .field('_id,username,nickname,department_id')
            .limit(10)
            .get();

          const users = userRes.result.data || [];

          // 为每个用户获取部门信息
          const userResults = [];
          for (const user of users) {
            const departmentName = await this.getDepartmentPath(user.department_id);

            userResults.push({
              ...user,
              departmentName
            });
          }

          this.userSearchResults = userResults;

          if (this.userSearchResults.length === 0) {
            uni.showToast({
              title: '未找到匹配的用户',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('搜索用户失败:', error);
          uni.showToast({
            title: '搜索用户失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      },

      /**
       * 选择用户
       */
      selectUser(user) {
        this.formData.assignedTo = user.username || user.nickname;
        this.formData.assignedToDept = user.departmentName || '该用户没有所属部门';

        // 清空搜索结果
        this.userSearchResults = [];
        this.userSearchText = user.username || user.nickname;

        uni.showToast({
          title: '处理人选择成功',
          icon: 'success'
        });
      },

      /**
       * 验证表单并提交
       */
      submit() {
        uni.showLoading({
          mask: true
        })
        this.$refs.form.validate().then((res) => {
          return this.submitForm(res)
        }).catch(() => {
        }).finally(() => {
          uni.hideLoading()
        })
      },

      /**
       * 提交表单
       */
      submitForm(value) {
        // 设置更新时间
        value.updateDate = Date.now();

        // 移除不可修改的字段
        delete value.orderNo;
        delete value.childOrderNo;
        delete value.createDate;

        // 使用 clientDB 提交数据
        return db.collection(dbCollectionName).doc(this.formDataId).update(value).then(() => {
          uni.showToast({
            title: '修改成功'
          })
          this.getOpenerEventChannel().emit('refreshData')
          setTimeout(() => uni.navigateBack(), 500)
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        })
      },

      /**
       * 获取表单数据
       * @param {Object} id
       */
      getDetail(id) {
        uni.showLoading({
          mask: true
        })
        db.collection(dbCollectionName).doc(id).field("orderNo,childOrderNo,orderDisplay,deviceNo,department,area,status,priority,creator,creatorDept,assignedTo,assignedToDept,description,workType,withMaterialNo,planStartTime,expectedTime,actualTime,attachments,remarks,createDate,updateDate,startTime,completeTime,closeTime").get().then((res) => {
          const data = res.result.data[0]
          if (data) {
            this.formData = data

            // 初始化搜索文本
            this.deviceSearchText = data.deviceNo || '';
            this.userSearchText = data.assignedTo || '';

            // 如果工单类型是维修且有设备编号，加载物料选项
            if (data.workType === '维修' && data.deviceNo) {
              this.loadMaterialOptions();
            }
          }
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        }).finally(() => {
          uni.hideLoading()
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.search-results {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-top: 5px;
}

.search-result-item {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

.device-no, .user-name {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 2px;
}

.device-info, .user-info {
  font-size: 12px;
  color: #666;
  display: block;
}

.selected-device,
.selected-user {
  margin-top: 5px;
  padding: 8px;
  background-color: #e8f5e8;
  border-radius: 4px;

  text {
    color: #52c41a;
    font-size: 14px;
  }
}

.material-selection {
  .no-material-tip, .no-device-tip {
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-top: 10px;

    text {
      color: #999;
      font-size: 14px;
    }
  }
}
</style>
