<template>
  <view>
    <view class="uni-header">
      <view class="uni-group">
        <view class="uni-title"></view>
        <view class="uni-sub-title"></view>
      </view>
      <view class="uni-group">
        <input class="uni-search" type="text" v-model="query" @confirm="search" placeholder="请输入搜索内容" />
        <button class="uni-button" type="default" size="mini" @click="search">搜索</button>
        <button class="uni-button" type="default" size="mini" @click="navigateTo('./add')">新增</button>
        <button class="uni-button" type="default" size="mini" :disabled="!selectedIndexs.length" @click="delTable">批量删除</button>
        <download-excel class="hide-on-phone" :fields="exportExcel.fields" :data="exportExcelData" :type="exportExcel.type" :name="exportExcel.filename">
          <button class="uni-button" type="primary" size="mini">导出 Excel</button>
        </download-excel>
      </view>
    </view>
    <view class="uni-container">
      <unicloud-db ref="udb" :collection="collectionList" field="orderNo,childOrderNo,deviceNo,department,area,status,priority,creator,assignedTo,workType,planStartTime" :where="where" page-data="replace"
        :orderby="orderby" :getcount="true" :page-size="options.pageSize" :page-current="options.pageCurrent"
        v-slot:default="{data,pagination,loading,error,options}" :options="options" loadtime="manual" @load="onqueryload">
        <uni-table ref="table" :loading="loading" :emptyText="error.message || '没有更多数据'" border stripe type="selection" @selection-change="selectionChange">
          <uni-tr>
            <uni-th align="center" filter-type="search" @filter-change="filterChange($event, 'orderNo')" sortable @sort-change="sortChange($event, 'orderNo')">工单编号</uni-th>
            <uni-th align="center" filter-type="search" @filter-change="filterChange($event, 'childOrderNo')" sortable @sort-change="sortChange($event, 'childOrderNo')">子工单编号</uni-th>
            <uni-th align="center" sortable @sort-change="sortChange($event, 'deviceNo')">设备编号</uni-th>
            <uni-th align="center" sortable @sort-change="sortChange($event, 'department')">所属部门</uni-th>
            <uni-th align="center" sortable @sort-change="sortChange($event, 'area')">区域</uni-th>
            <uni-th align="center" filter-type="select" :filter-data="options.filterData.status_localdata" @filter-change="filterChange($event, 'status')">工单状态</uni-th>
            <uni-th align="center" filter-type="select" :filter-data="options.filterData.priority_localdata" @filter-change="filterChange($event, 'priority')">优先级</uni-th>
            <uni-th align="center" sortable @sort-change="sortChange($event, 'creator')">工单创建人</uni-th>
            <uni-th align="center" sortable @sort-change="sortChange($event, 'assignedTo')">处理人</uni-th>
            <uni-th align="center" filter-type="select" :filter-data="options.filterData.workType_localdata" @filter-change="filterChange($event, 'workType')">工单类型</uni-th>
            <uni-th align="center" filter-type="timestamp" @filter-change="filterChange($event, 'planStartTime')" sortable @sort-change="sortChange($event, 'planStartTime')">计划开始时间</uni-th>
            <uni-th align="center">操作</uni-th>
          </uni-tr>
          <uni-tr v-for="(item,index) in data" :key="index">
            <uni-td align="center">{{item.orderNo}}</uni-td>
            <uni-td align="center">{{item.childOrderNo || '-'}}</uni-td>
            <uni-td align="center">{{item.deviceNo}}</uni-td>
            <uni-td align="center">{{item.department}}</uni-td>
            <uni-td align="center">{{item.area}}</uni-td>
            <uni-td align="center">{{options.status_valuetotext[item.status]}}</uni-td>
            <uni-td align="center">{{options.priority_valuetotext[item.priority]}}</uni-td>
            <uni-td align="center">{{item.creator}}</uni-td>
            <uni-td align="center">{{item.assignedTo}}</uni-td>
            <uni-td align="center">{{options.workType_valuetotext[item.workType]}}</uni-td>
            <uni-td align="center">
              <uni-dateformat v-if="item.planStartTime" :threshold="[0, 0]" :date="item.planStartTime"></uni-dateformat>
              <text v-else>-</text>
            </uni-td>
            <uni-td align="center">
              <view class="uni-group">
                <button @click="navigateTo('./edit?id='+item._id, false)" class="uni-button" size="mini" type="primary">修改</button>
                <button @click="confirmDelete(item._id)" class="uni-button" size="mini" type="warn">删除</button>
              </view>
            </uni-td>
          </uni-tr>
        </uni-table>
        <view class="uni-pagination-box">
          <uni-pagination show-icon :page-size="pagination.size" v-model="pagination.current" :total="pagination.count" @change="onPageChanged" />
        </view>
      </unicloud-db>
    </view>
  </view>
</template>

<script>
  import { enumConverter, filterToWhere } from '../../js_sdk/validator/WorkOrder.js';

  const db = uniCloud.database()
  // 表查询配置
  const dbOrderBy = '' // 排序字段
  const dbSearchFields = [] // 模糊搜索字段，支持模糊搜索的字段列表。联表查询格式: 主表字段名.副表字段名，例如用户表关联角色表 role.role_name
  // 分页配置
  const pageSize = 20
  const pageCurrent = 1

  const orderByMapping = {
    "ascending": "asc",
    "descending": "desc"
  }

  export default {
    data() {
      return {
        collectionList: "WorkOrder",
        query: '',
        where: '',
        orderby: dbOrderBy,
        orderByFieldName: "",
        selectedIndexs: [],
        options: {
          pageSize,
          pageCurrent,
          filterData: {
            "status_localdata": [
              {
                "value": "待处理",
                "text": "待处理"
              },
              {
                "value": "处理中",
                "text": "处理中"
              },
              {
                "value": "已完成",
                "text": "已完成"
              },
              {
                "value": "已关闭",
                "text": "已关闭"
              },
              {
                "value": "已取消",
                "text": "已取消"
              },
              {
                "value": "待评估",
                "text": "待评估"
              },
              {
                "value": "待验收",
                "text": "待验收"
              }
            ],
            "priority_localdata": [
              {
                "value": "高",
                "text": "高"
              },
              {
                "value": "中",
                "text": "中"
              },
              {
                "value": "低",
                "text": "低"
              }
            ],
            "workType_localdata": [
              {
                "value": "维修",
                "text": "维修"
              },
              {
                "value": "预防性维修",
                "text": "预防性维修"
              },
              {
                "value": "保养",
                "text": "保养"
              },
              {
                "value": "检查",
                "text": "检查"
              },
              {
                "value": "安装",
                "text": "安装"
              },
              {
                "value": "调试",
                "text": "调试"
              },
              {
                "value": "其他",
                "text": "其他"
              }
            ]
          },
          ...enumConverter
        },
        imageStyles: {
          width: 64,
          height: 64
        },
        exportExcel: {
          "filename": "WorkOrder.xls",
          "type": "xls",
          "fields": {
            "工单编号": "orderNo",
            "子工单编号": "childOrderNo",
            "设备编号": "deviceNo",
            "所属部门": "department",
            "区域": "area",
            "工单状态": "status",
            "优先级": "priority",
            "工单创建人": "creator",
            "处理人": "assignedTo",
            "工单类型": "workType",
            "计划开始时间": "planStartTime"
          }
        },
        exportExcelData: []
      }
    },
    onLoad() {
      this._filter = {}
    },
    onReady() {
      this.$refs.udb.loadData()
    },
    methods: {
      onqueryload(data) {
        this.exportExcelData = data
      },
      getWhere() {
        const query = this.query.trim()
        if (!query) {
          return ''
        }
        const queryRe = new RegExp(query, 'i')
        return dbSearchFields.map(name => queryRe + '.test(' + name + ')').join(' || ')
      },
      search() {
        const newWhere = this.getWhere()
        this.where = newWhere
        this.$nextTick(() => {
          this.loadData()
        })
      },
      loadData(clear = true) {
        this.$refs.udb.loadData({
          clear
        })
      },
      onPageChanged(e) {
        this.selectedIndexs.length = 0
        this.$refs.table.clearSelection()
        this.$refs.udb.loadData({
          current: e.current
        })
      },
      navigateTo(url, clear) {
        // clear 表示刷新列表时是否清除页码，true 表示刷新并回到列表第 1 页，默认为 true
        uni.navigateTo({
          url,
          events: {
            refreshData: () => {
              this.loadData(clear)
            }
          }
        })
      },
      // 多选处理
      selectedItems() {
        var dataList = this.$refs.udb.dataList
        return this.selectedIndexs.map(i => dataList[i]._id)
      },
      // 批量删除
      delTable() {
        this.$refs.udb.remove(this.selectedItems(), {
          success:(res) => {
            this.$refs.table.clearSelection()
          }
        })
      },
      // 多选
      selectionChange(e) {
        this.selectedIndexs = e.detail.index
      },
      confirmDelete(id) {
        this.$refs.udb.remove(id, {
          success:(res) => {
            this.$refs.table.clearSelection()
          }
        })
      },
      sortChange(e, name) {
        this.orderByFieldName = name;
        if (e.order) {
          this.orderby = name + ' ' + orderByMapping[e.order]
        } else {
          this.orderby = ''
        }
        this.$refs.table.clearSelection()
        this.$nextTick(() => {
          this.$refs.udb.loadData()
        })
      },
      filterChange(e, name) {
        this._filter[name] = {
          type: e.filterType,
          value: e.filter
        }
        let newWhere = filterToWhere(this._filter, db.command)
        if (Object.keys(newWhere).length) {
          this.where = newWhere
        } else {
          this.where = ''
        }
        this.$nextTick(() => {
          this.$refs.udb.loadData()
        })
      }
    }
  }
</script>

<style>
</style>
