// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "materialNo": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ]
  },
  "childMaterialNo": {
    "rules": [
      {
        "format": "array"
      }
    ]
  },
  "deviceName": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "status": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "有效",
            "text": "有效"
          },
          {
            "value": "无效",
            "text": "无效"
          },
          {
            "value": "更新中",
            "text": "更新中"
          },
          {
            "value": "已归档",
            "text": "已归档"
          },
          {
            "value": "待审核",
            "text": "待审核"
          }
        ]
      }
    ]
  },
  "documentUrl": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ]
  },
  "model": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "型号",
    "label": "型号"
  },
  "Type": {
    "rules": [
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "零件",
            "text": "零件"
          },
          {
            "value": "整机",
            "text": "整机"
          },
          {
            "value": "图纸",
            "text": "图纸"
          },
          {
            "value": "使用手册",
            "text": "使用手册"
          },
          {
            "value": "维修手册",
            "text": "维修手册"
          },
          {
            "value": "测试报告",
            "text": "测试报告"
          },
          {
            "value": "质检报告",
            "text": "质检报告"
          },
          {
            "value": "其他",
            "text": "其他"
          }
        ]
      }
    ]
  },
  "documentDetails": {
    "rules": [
      {
        "format": "array"
      }
    ]
  },
  "version": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "uploadBy": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "description": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "createDate": {
    "rules": [
      {
        "format": "timestamp"
      }
    ]
  },
  "updateDate": {
    "rules": [
      {
        "format": "timestamp"
      }
    ]
  }
}

const enumConverter = {
  "status_valuetotext": {
    "有效": "有效",
    "无效": "无效",
    "更新中": "更新中",
    "已归档": "已归档",
    "待审核": "待审核"
  },
  "Type_valuetotext": {
    "零件": "零件",
    "整机": "整机",
    "图纸": "图纸",
    "使用手册": "使用手册",
    "维修手册": "维修手册",
    "测试报告": "测试报告",
    "质检报告": "质检报告",
    "其他": "其他"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
