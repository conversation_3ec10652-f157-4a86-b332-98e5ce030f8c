{"bsonType": "object", "required": ["materialNo", "status", "documentUrl"], "permission": {"read": true, "create": true, "update": true, "delete": true}, "properties": {"_id": {"description": "ID，系统自动生成"}, "materialNo": {"bsonType": "string", "description": "物料号"}, "childMaterialNo": {"bsonType": "array", "description": "子级物料号，如果没有子级则为空", "foreignKey": "DeviceDocuments.materialNo"}, "deviceName": {"bsonType": "string", "description": "设备名称"}, "status": {"bsonType": "string", "description": "设备资料状态", "enum": ["有效", "无效", "更新中", "已归档", "待审核"]}, "documentUrl": {"bsonType": "string", "description": "资料链接地址"}, "model": {"bsonType": "string", "title": "型号", "description": "设备的型号"}, "Type": {"bsonType": "string", "description": "设备类型", "enum": ["零件", "整机", "图纸", "使用手册", "维修手册", "测试报告", "质检报告", "其他"]}, "documentDetails": {"bsonType": "array", "description": "设备资料详情", "items": {"bsonType": "object", "required": ["name", "value"], "properties": {"name": {"bsonType": "string", "description": "自定义字段名"}, "value": {"bsonType": "string", "description": "自定义字段值"}}}}, "version": {"bsonType": "string", "description": "资料版本号"}, "uploadBy": {"bsonType": "string", "description": "上传人"}, "description": {"bsonType": "string", "description": "资料描述"}, "createDate": {"bsonType": "timestamp", "description": "创建时间"}, "updateDate": {"bsonType": "timestamp", "description": "更新时间"}}}